import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Platform,
  Alert,
  Dimensions,
  StatusBar,
  Share,
  Image,
  UIManager,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import * as Clipboard from 'expo-clipboard';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInUp,
  SlideInLeft,
  SlideInRight,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  withSequence,
  withDelay,
  withRepeat,
  Easing,
  useAnimatedScrollHandler,
  interpolateColor,
} from 'react-native-reanimated';

import { Colors, Spacing, BorderRadius, FontSizes, FontWeights, Shadows } from '../constants/Colors';
import ApiService from '../services/ApiService';
import ScanHistoryService from '../services/ScanHistoryService';
import VoiceService, { VoiceServiceCallbacks } from '../services/VoiceService';

const { width, height } = Dimensions.get('window');

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// Premium Design Constants
const PREMIUM_COLORS = {
  primary: '#6B7C5A',
  secondary: '#8B9A7A',
  background: '#FFFFFF',
  surface: '#F8F9FA',
  userBubble: '#6B7C5A',
  aiBubble: ['#F8F9FA', '#FFFFFF'],
  text: '#1F2937',
  textSecondary: '#6B7280',
  border: '#E5E7EB',
  shadow: 'rgba(0, 0, 0, 0.1)',
  success: '#10B981',
  error: '#EF4444',
  warning: '#F59E0B',
};

const ANIMATION_CONFIG = {
  spring: {
    damping: 20,
    stiffness: 300,
    mass: 1,
  },
  timing: {
    duration: 300,
    easing: Easing.bezier(0.4, 0, 0.2, 1),
  },
  bounce: {
    damping: 15,
    stiffness: 400,
    mass: 0.8,
  },
};

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  typing?: boolean;
  suggestions?: string[];
  keyPoints?: string[];
  recommendations?: string[];
  tips?: string[];
  relatedTopics?: string[];
  confidence?: number;
  category?: string;
  followUpQuestions?: string[];
}

interface MessageBubbleProps {
  message: ChatMessage;
  onSuggestionPress?: (suggestion: string) => void;
}

interface QuickQuestionProps {
  question: {
    id: string;
    text: string;
    icon: keyof typeof Ionicons.glyphMap;
    category?: string;
  };
  onPress: () => void;
  index: number;
}

interface VoiceInputProps {
  isListening: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  voiceText: string;
}

interface TypingIndicatorProps {
  visible: boolean;
}

// Premium Typing Indicator Component with 60fps animations
const TypingIndicator: React.FC<TypingIndicatorProps> = ({ visible }) => {
  const dot1 = useSharedValue(0.4);
  const dot2 = useSharedValue(0.4);
  const dot3 = useSharedValue(0.4);
  const containerScale = useSharedValue(0);
  const containerOpacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      // Entrance animation
      containerScale.value = withSpring(1, ANIMATION_CONFIG.spring);
      containerOpacity.value = withTiming(1, ANIMATION_CONFIG.timing);

      // Continuous dot animation with premium easing
      const animateDots = () => {
        dot1.value = withSequence(
          withTiming(1, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
          withTiming(0.4, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) })
        );
        dot2.value = withDelay(150, withSequence(
          withTiming(1, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
          withTiming(0.4, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) })
        ));
        dot3.value = withDelay(300, withSequence(
          withTiming(1, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) }),
          withTiming(0.4, { duration: 500, easing: Easing.bezier(0.4, 0, 0.2, 1) })
        ));
      };

      const interval = setInterval(animateDots, 1400);
      animateDots();

      return () => clearInterval(interval);
    } else {
      // Exit animation
      containerScale.value = withSpring(0.8, ANIMATION_CONFIG.spring);
      containerOpacity.value = withTiming(0, ANIMATION_CONFIG.timing);
    }
  }, [visible]);

  const containerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: containerScale.value }],
    opacity: containerOpacity.value,
  }));

  const dot1Style = useAnimatedStyle(() => ({
    opacity: dot1.value,
    transform: [{
      scale: interpolate(dot1.value, [0.4, 1], [0.6, 1], 'clamp'),
      translateY: interpolate(dot1.value, [0.4, 1], [0, -2], 'clamp')
    }],
  }));

  const dot2Style = useAnimatedStyle(() => ({
    opacity: dot2.value,
    transform: [{
      scale: interpolate(dot2.value, [0.4, 1], [0.6, 1], 'clamp'),
      translateY: interpolate(dot2.value, [0.4, 1], [0, -2], 'clamp')
    }],
  }));

  const dot3Style = useAnimatedStyle(() => ({
    opacity: dot3.value,
    transform: [{
      scale: interpolate(dot3.value, [0.4, 1], [0.6, 1], 'clamp'),
      translateY: interpolate(dot3.value, [0.4, 1], [0, -2], 'clamp')
    }],
  }));

  if (!visible) return null;

  return (
    <Animated.View style={[styles.premiumTypingContainer, containerStyle]} entering={SlideInUp.duration(300)} exiting={FadeInDown.duration(200)}>
      <View style={styles.premiumAiAvatar}>
        <LinearGradient
          colors={['#6B7C5A', '#8B9A7A']}
          style={styles.premiumAvatarGradient}
        >
          <Image
            source={require('../../assets/image final.png')}
            style={styles.premiumAvatarLogo}
            resizeMode="contain"
          />
        </LinearGradient>
      </View>
      <View style={styles.premiumTypingBubble}>
        <LinearGradient
          colors={['#F8F9FA', '#FFFFFF']}
          style={styles.premiumTypingGradient}
        >
          <View style={styles.premiumTypingDots}>
            <Animated.View style={[styles.premiumTypingDot, dot1Style]} />
            <Animated.View style={[styles.premiumTypingDot, dot2Style]} />
            <Animated.View style={[styles.premiumTypingDot, dot3Style]} />
          </View>
        </LinearGradient>
      </View>
    </Animated.View>
  );
};

// Modern Voice Input Component
const VoiceInput: React.FC<VoiceInputProps> = ({
  isListening,
  onStartListening,
  onStopListening,
  voiceText,
}) => {
  const scale = useSharedValue(1);
  const waveScale1 = useSharedValue(1);
  const waveScale2 = useSharedValue(1);
  const waveScale3 = useSharedValue(1);

  useEffect(() => {
    if (isListening) {
      // Gentle breathing animation for main button
      scale.value = withRepeat(
        withSequence(
          withTiming(1.05, { duration: 1200 }),
          withTiming(1, { duration: 1200 })
        ),
        -1,
        true
      );

      // Elegant wave animations with staggered timing
      waveScale1.value = withRepeat(
        withSequence(
          withTiming(1.4, { duration: 1500 }),
          withTiming(1, { duration: 1500 })
        ),
        -1,
        true
      );

      waveScale2.value = withRepeat(
        withSequence(
          withDelay(500, withTiming(1.6, { duration: 1500 })),
          withTiming(1, { duration: 1500 })
        ),
        -1,
        true
      );

      waveScale3.value = withRepeat(
        withSequence(
          withDelay(1000, withTiming(1.8, { duration: 1500 })),
          withTiming(1, { duration: 1500 })
        ),
        -1,
        true
      );
    } else {
      scale.value = withTiming(1, { duration: 300 });
      waveScale1.value = withTiming(1, { duration: 300 });
      waveScale2.value = withTiming(1, { duration: 300 });
      waveScale3.value = withTiming(1, { duration: 300 });
    }
  }, [isListening]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const wave1Style = useAnimatedStyle(() => ({
    transform: [{ scale: waveScale1.value }],
    opacity: interpolate(waveScale1.value, [1, 1.4], [0, 0.2]),
  }));

  const wave2Style = useAnimatedStyle(() => ({
    transform: [{ scale: waveScale2.value }],
    opacity: interpolate(waveScale2.value, [1, 1.6], [0, 0.15]),
  }));

  const wave3Style = useAnimatedStyle(() => ({
    transform: [{ scale: waveScale3.value }],
    opacity: interpolate(waveScale3.value, [1, 1.8], [0, 0.1]),
  }));

  return (
    <View style={styles.voiceInputContainer}>
      {/* Modern Background Blur */}
      <View style={styles.voiceBackdrop} />

      {/* Voice Text Display */}
      {voiceText && (
        <Animated.View entering={SlideInUp.duration(400)} style={styles.voiceTextContainer}>
          <View style={styles.voiceTextHeader}>
            <Ionicons name="mic" size={16} color="#6B7C5A" />
            <Text style={styles.voiceTextLabel}>Listening...</Text>
          </View>
          <Text style={styles.voiceText}>{voiceText}</Text>
        </Animated.View>
      )}

      {/* Modern Voice Button with Elegant Waves */}
      <View style={styles.voiceButtonContainer}>
        {/* Animated Waves */}
        <Animated.View style={[styles.voiceWave, styles.voiceWave1, wave1Style]} />
        <Animated.View style={[styles.voiceWave, styles.voiceWave2, wave2Style]} />
        <Animated.View style={[styles.voiceWave, styles.voiceWave3, wave3Style]} />

        {/* Main Voice Button */}
        <Animated.View style={[styles.voiceButton, animatedStyle]}>
          <TouchableOpacity
            style={[styles.voiceButtonInner, isListening && styles.voiceButtonListening]}
            onPress={isListening ? onStopListening : onStartListening}
            onPressIn={() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)}
          >
            <Ionicons
              name={isListening ? "stop" : "mic"}
              size={28}
              color="#FFFFFF"
            />
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Instructions */}
      <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.voiceInstructions}>
        <Text style={styles.voiceInstructionText}>
          {isListening ? 'Tap to stop recording' : 'Tap to start voice input'}
        </Text>
      </Animated.View>
    </View>
  );
};

// Quick Question Component
const QuickQuestion: React.FC<QuickQuestionProps> = ({ question, onPress, index }) => {
  const scale = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 400 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 400 });
  };

  return (
    <Animated.View
      entering={SlideInRight.delay(index * 100).duration(500)}
      style={[styles.quickQuestion, animatedStyle]}
    >
      <TouchableOpacity
        style={styles.quickQuestionButton}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        <View style={styles.quickQuestionIcon}>
          <Ionicons name={question.icon} size={20} color={Colors.brand} />
        </View>
        <Text style={styles.quickQuestionText}>{question.text}</Text>
        <Ionicons name="chevron-forward" size={16} color={Colors.mutedForeground} />
      </TouchableOpacity>
    </Animated.View>
  );
};

const AskScreenModern: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [loading, setLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');
  const [typingText, setTypingText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Premium animation values
  const scrollY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  // Cursor blinking animation
  const cursorOpacity = useSharedValue(1);

  useEffect(() => {
    if (isTyping) {
      cursorOpacity.value = withRepeat(
        withSequence(
          withTiming(0, { duration: 500 }),
          withTiming(1, { duration: 500 })
        ),
        -1,
        true
      );
    } else {
      cursorOpacity.value = 0;
    }
  }, [isTyping]);

  const cursorAnimatedStyle = useAnimatedStyle(() => ({
    opacity: cursorOpacity.value,
  }));

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const quickQuestions = [
    { id: '1', text: 'How much protein should I eat daily?', icon: 'fitness' as const, category: 'Nutrition' },
    { id: '2', text: 'What are the best foods for energy?', icon: 'flash' as const, category: 'Energy' },
    { id: '3', text: 'How can I reduce sugar cravings?', icon: 'heart' as const, category: 'Health' },
    { id: '4', text: 'What vitamins do I need?', icon: 'medical' as const, category: 'Supplements' },
    { id: '5', text: 'How to meal prep effectively?', icon: 'time' as const, category: 'Planning' },
    { id: '6', text: 'Best foods for better sleep?', icon: 'moon' as const, category: 'Wellness' },
    { id: '7', text: 'How to build muscle with diet?', icon: 'barbell' as const, category: 'Fitness' },
    { id: '8', text: 'What foods boost metabolism?', icon: 'speedometer' as const, category: 'Weight' },
  ];

// ✨ CLEAN MODERN AI RESPONSE RENDERER
const renderCleanModernResponse = (message: ChatMessage) => {
  const formatResponseText = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim());
    const elements = [];
    let currentIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Handle headings (##text)
      if (line.startsWith('##')) {
        const headingText = line.replace(/^##\s*/, '').trim();
        elements.push(
          <View key={currentIndex++} style={styles.headingContainer}>
            <Ionicons name="chevron-forward" size={16} color="#6B7C5A" />
            <Text style={styles.headingText}>{headingText}</Text>
          </View>
        );
        continue;
      }

      // Handle bold text (**text**)
      if (line.includes('**')) {
        const parts = line.split(/(\*\*[^*]+\*\*)/);
        elements.push(
          <View key={currentIndex++} style={styles.paragraphContainer}>
            <Text style={styles.responseText}>
              {parts.map((part, partIndex) => {
                if (part.startsWith('**') && part.endsWith('**')) {
                  const boldText = part.replace(/\*\*/g, '');
                  return (
                    <Text key={partIndex} style={styles.boldText}>
                      {boldText}
                    </Text>
                  );
                }
                return part;
              })}
            </Text>
          </View>
        );
        continue;
      }

      // Handle parenthetical text (text)
      if (line.includes('(') && line.includes(')')) {
        const parts = line.split(/(\([^)]+\))/);
        elements.push(
          <View key={currentIndex++} style={styles.paragraphContainer}>
            <Text style={styles.responseText}>
              {parts.map((part, partIndex) => {
                if (part.startsWith('(') && part.endsWith(')')) {
                  const noteText = part.replace(/[()]/g, '');
                  return (
                    <Text key={partIndex} style={styles.noteText}>
                      {part}
                    </Text>
                  );
                }
                return part;
              })}
            </Text>
          </View>
        );
        continue;
      }

      // Handle lists (-, •, 1., etc.)
      if (/^[-•*]\s/.test(line) || /^\d+\.\s/.test(line)) {
        const cleanItem = line.replace(/^[-•*]\s/, '').replace(/^\d+\.\s/, '').trim();
        elements.push(
          <View key={currentIndex++} style={styles.cleanListItem}>
            <View style={styles.listDot} />
            <Text style={styles.listText}>{cleanItem}</Text>
          </View>
        );
        continue;
      }

      // Regular paragraph
      elements.push(
        <View key={currentIndex++} style={styles.paragraphContainer}>
          <Text style={styles.responseText}>{line}</Text>
        </View>
      );
    }

    return elements;
  };

  return (
    <View
      style={styles.cleanResponseContainer}
    >
      {/* AI Avatar and Header */}
      <View style={styles.responseHeader}>
        <View style={styles.cleanAiAvatar}>
          <Image
            source={require('../../assets/image final.png')}
            style={styles.cleanAiAvatarLogo}
            resizeMode="contain"
          />
        </View>
        <View style={styles.responseInfo}>
          <Text style={styles.aiLabel}>NutriAI</Text>
          <Text style={styles.responseTimestamp}>
            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </View>

      {/* Response Content */}
      <View style={styles.responseContent}>
        {formatResponseText(message.text)}
      </View>

      {/* Action Buttons */}
      <View style={styles.responseActions}>
        <TouchableOpacity
          style={styles.actionBtn}
          onPress={() => handleCopyResponse(message.text)}
        >
          <Ionicons name="copy-outline" size={16} color="#6B7C5A" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionBtn}
          onPress={handleLikeResponse}
        >
          <Ionicons name="thumbs-up-outline" size={16} color="#6B7C5A" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionBtn}
          onPress={() => handleShareResponse(message.text)}
        >
          <Ionicons name="share-outline" size={16} color="#6B7C5A" />
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Removed redundant legacy functions and unused parseResponseSections

// Premium Message Bubble Component with 60fps animations
const PremiumMessageBubble: React.FC<MessageBubbleProps> = React.memo(({ message }) => {
  const scale = useSharedValue(0.8);
  const opacity = useSharedValue(0);
  const translateY = useSharedValue(20);

  useEffect(() => {
    // Entrance animation with premium easing
    scale.value = withSpring(1, ANIMATION_CONFIG.spring);
    opacity.value = withTiming(1, ANIMATION_CONFIG.timing);
    translateY.value = withSpring(0, ANIMATION_CONFIG.spring);
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateY: translateY.value }
    ],
    opacity: opacity.value,
  }));

  const formatTimestamp = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Enhanced text formatting function for AI responses
  const formatResponseText = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim());
    const elements = [];
    let currentIndex = 0;

    for (const line of lines) {
      // Handle headers (## text)
      if (line.startsWith('##')) {
        const headerText = line.replace(/^##\s*/, '').trim();
        elements.push(
          <Text key={currentIndex++} style={styles.premiumResponseHeader}>
            {headerText}
          </Text>
        );
        continue;
      }

      // Handle bold text (**text**)
      if (line.includes('**')) {
        const parts = line.split(/(\*\*[^*]+\*\*)/);
        elements.push(
          <Text key={currentIndex++} style={styles.premiumAiText}>
            {parts.map((part, partIndex) => {
              if (part.startsWith('**') && part.endsWith('**')) {
                const boldText = part.replace(/\*\*/g, '');
                return (
                  <Text key={partIndex} style={styles.premiumBoldText}>
                    {boldText}
                  </Text>
                );
              }
              return part;
            })}
          </Text>
        );
        continue;
      }

      // Handle lists (-, •, 1., etc.)
      if (/^[-•*]\s/.test(line) || /^\d+\.\s/.test(line)) {
        const cleanItem = line.replace(/^[-•*]\s/, '').replace(/^\d+\.\s/, '').trim();
        elements.push(
          <View key={currentIndex++} style={styles.premiumListItem}>
            <View style={styles.premiumListDot} />
            <Text style={styles.premiumListText}>{cleanItem}</Text>
          </View>
        );
        continue;
      }

      // Regular paragraph
      elements.push(
        <Text key={currentIndex++} style={styles.premiumAiText}>
          {line}
        </Text>
      );
    }

    return elements;
  };

  if (message.isUser) {
    return (
      <Animated.View style={[styles.premiumMessageContainer, animatedStyle]} entering={SlideInRight.duration(300)}>
        <View style={styles.premiumUserBubble}>
          <Text style={styles.premiumUserText}>{message.text}</Text>
          <Text style={styles.premiumTimestamp}>{formatTimestamp(message.timestamp)}</Text>
        </View>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.premiumMessageContainer, animatedStyle]} entering={SlideInLeft.duration(300)}>
      <View style={styles.premiumAiBubbleContainer}>
        <View style={styles.premiumAiAvatar}>
          <LinearGradient
            colors={['#6B7C5A', '#8B9A7A']}
            style={styles.premiumAvatarGradient}
          >
            <Image
              source={require('../../assets/image final.png')}
              style={styles.premiumAvatarLogo}
              resizeMode="contain"
            />
          </LinearGradient>
        </View>
        <View style={styles.premiumAiBubble}>
          <LinearGradient
            colors={['#F8F9FA', '#FFFFFF']}
            style={styles.premiumAiBubbleGradient}
          >
            {formatResponseText(message.text)}
            <Text style={styles.premiumTimestamp}>{formatTimestamp(message.timestamp)}</Text>

            {/* Premium Action Buttons */}
            <View style={styles.premiumMessageActions}>
              <PremiumActionButton
                icon="copy-outline"
                onPress={() => handleCopyResponse(message.text)}
              />
              <PremiumActionButton
                icon="thumbs-up-outline"
                onPress={handleLikeResponse}
              />
              <PremiumActionButton
                icon="share-outline"
                onPress={() => handleShareResponse(message.text)}
              />
            </View>
          </LinearGradient>
        </View>
      </View>
    </Animated.View>
  );
}, (prevProps, nextProps) => {
  return prevProps.message.id === nextProps.message.id &&
         prevProps.message.text === nextProps.message.text;
});

// Premium Action Button Component
const PremiumActionButton: React.FC<{ icon: string; onPress: () => void }> = ({ icon, onPress }) => {
  const scale = useSharedValue(1);
  const backgroundColor = useSharedValue(0);

  const handlePressIn = () => {
    scale.value = withSpring(0.9, ANIMATION_CONFIG.bounce);
    backgroundColor.value = withTiming(1, ANIMATION_CONFIG.timing);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, ANIMATION_CONFIG.bounce);
    backgroundColor.value = withTiming(0, ANIMATION_CONFIG.timing);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    backgroundColor: interpolateColor(
      backgroundColor.value,
      [0, 1],
      ['#F9FAFB', '#6B7C5A']
    ),
  }));

  const iconStyle = useAnimatedStyle(() => ({
    color: interpolateColor(
      backgroundColor.value,
      [0, 1],
      ['#6B7C5A', '#FFFFFF']
    ),
  }));

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={1}
    >
      <Animated.View style={[styles.premiumActionButton, animatedStyle]}>
        <Animated.Text style={iconStyle}>
          <Ionicons name={icon as any} size={16} />
        </Animated.Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

// Premium Input Section Component
interface PremiumInputSectionProps {
  inputText: string;
  setInputText: (text: string) => void;
  loading: boolean;
  isListening: boolean;
  onSendMessage: () => void;
  onStartVoice: () => void;
  onStopVoice: () => void;
}

const PremiumInputSection: React.FC<PremiumInputSectionProps> = React.memo(({
  inputText,
  setInputText,
  loading,
  isListening,
  onSendMessage,
  onStartVoice,
  onStopVoice,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const textInputRef = useRef<TextInput>(null);
  const borderColor = useSharedValue(0);
  const sendButtonScale = useSharedValue(1);
  const voiceButtonScale = useSharedValue(1);

  const handleFocus = () => {
    setIsFocused(true);
    borderColor.value = withTiming(1, ANIMATION_CONFIG.timing);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const handleBlur = () => {
    setIsFocused(false);
    borderColor.value = withTiming(0, ANIMATION_CONFIG.timing);
  };

  const handleSendPress = () => {
    sendButtonScale.value = withSequence(
      withSpring(0.9, ANIMATION_CONFIG.bounce),
      withSpring(1, ANIMATION_CONFIG.bounce)
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onSendMessage();
  };

  const handleVoicePress = () => {
    voiceButtonScale.value = withSequence(
      withSpring(0.9, ANIMATION_CONFIG.bounce),
      withSpring(1, ANIMATION_CONFIG.bounce)
    );
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    isListening ? onStopVoice() : onStartVoice();
  };

  // Simplified container style without problematic keyboard animations
  const containerStyle = useAnimatedStyle(() => ({
    // Remove keyboard transform that was causing input focus issues
  }));

  const wrapperStyle = useAnimatedStyle(() => ({
    borderColor: interpolateColor(
      borderColor.value,
      [0, 1],
      ['#E5E7EB', '#6B7C5A']
    ),
    backgroundColor: interpolateColor(
      borderColor.value,
      [0, 1],
      ['#F9FAFB', '#FFFFFF']
    ),
    shadowOpacity: interpolate(borderColor.value, [0, 1], [0.05, 0.1]),
  }));

  const sendButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: sendButtonScale.value }],
    opacity: inputText.trim() ? 1 : 0.5,
  }));

  const voiceButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: voiceButtonScale.value }],
    backgroundColor: isListening ? '#EF4444' : '#8B9A7A',
  }));

  return (
    <Animated.View style={[styles.premiumInputContainer, containerStyle]}>
      <Animated.View style={[styles.premiumInputWrapper, wrapperStyle, isFocused && styles.premiumInputWrapperFocused]}>
        <TextInput
          key="ask-ai-input"
          ref={textInputRef}
          style={styles.premiumTextInput}
          placeholder="Ask me anything about nutrition..."
          placeholderTextColor="#9CA3AF"
          value={inputText}
          onChangeText={setInputText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          multiline
          maxLength={500}
          onSubmitEditing={handleSendPress}
          blurOnSubmit={false}
          returnKeyType="send"
          enablesReturnKeyAutomatically={true}
          autoCorrect={true}
          autoCapitalize="sentences"
          textAlignVertical="top"
        />

        <TouchableOpacity
          onPress={handleVoicePress}
          activeOpacity={0.8}
        >
          <Animated.View style={[styles.premiumVoiceButton, voiceButtonStyle, isListening && styles.premiumVoiceButtonActive]}>
            <Ionicons
              name={isListening ? "stop" : "mic"}
              size={16}
              color="white"
            />
          </Animated.View>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={handleSendPress}
          disabled={!inputText.trim() || loading}
          activeOpacity={0.8}
        >
          <Animated.View style={[styles.premiumSendButton, sendButtonStyle]}>
            {loading ? (
              <View style={{ width: 16, height: 16 }}>
                <Ionicons name="hourglass" size={16} color="white" />
              </View>
            ) : (
              <Ionicons name="send" size={16} color="white" />
            )}
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.inputText === nextProps.inputText &&
    prevProps.loading === nextProps.loading &&
    prevProps.isListening === nextProps.isListening &&
    prevProps.onSendMessage === nextProps.onSendMessage &&
    prevProps.onStartVoice === nextProps.onStartVoice &&
    prevProps.onStopVoice === nextProps.onStopVoice
  );
});

  // Improved typing animation with realistic speed
  const animateMessage = (text: string, callback: () => void) => {
    setIsTyping(true);

    // Start with empty text
    setTypingText('');

    // Calculate typing speed based on text length
    const typingSpeed = Math.max(10, Math.min(50, 1000 / (text.length / 20))); // Characters per second
    const charsPerFrame = Math.max(1, Math.floor(text.length / 100)); // Show more chars at once for longer texts

    let currentIndex = 0;

    // Create typing interval
    const typingInterval = setInterval(() => {
      if (currentIndex < text.length) {
        // Add next chunk of characters
        const nextChunk = text.substring(0, currentIndex + charsPerFrame);
        setTypingText(nextChunk);
        currentIndex += charsPerFrame;

        // Trigger haptic feedback occasionally for realism
        if (Math.random() < 0.05) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      } else {
        // Typing complete
        clearInterval(typingInterval);

        // Short delay before completing
        setTimeout(() => {
          setIsTyping(false);
          callback();
        }, 300);
      }
    }, typingSpeed);

    // Cleanup function
    return () => clearInterval(typingInterval);
  };

  const sendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      text: text.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    handleInputTextChange('');
    setLoading(true);

    try {
      const responseText = await ApiService.askNutritionQuestion(text);
      setLoading(false);

      // Start animation
      animateMessage(responseText, () => {
        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: responseText,
          isUser: false,
          timestamp: new Date(),
          // Remove JSON-dependent fields since we're using plain text response
          keyPoints: [],
          recommendations: [],
          tips: [],
          relatedTopics: [],
          confidence: undefined,
          category: undefined,
          followUpQuestions: [],
        };
        setMessages(prev => [...prev, aiMessage]);
        setTypingText('');

        // Scroll to show the new response after a short delay
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      });
    } catch (error) {
      console.error('Error asking question:', error);
      setLoading(false);

      // Fallback response with typing animation
      const fallbackText = "I'm here to help with your nutrition questions! While I'm currently having trouble connecting to my knowledge base, I'd recommend consulting with a registered dietitian for personalized advice. In the meantime, focus on eating a balanced diet with plenty of fruits, vegetables, lean proteins, and whole grains.";

      animateMessage(fallbackText, () => {
        const aiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          text: fallbackText,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, aiMessage]);
        setTypingText('');
      });
    }
  };



  const handleQuickQuestion = (question: string) => {
    sendMessage(question);
  };

  const startVoiceRecognition = async () => {
    try {
      const isInitialized = await VoiceService.initialize();
      if (!isInitialized) {
        Alert.alert(
          'Voice Recognition Unavailable',
          'Voice recognition is not available in this environment. Please:\n\n1. Open in web browser (press "w" in Expo CLI)\n2. Use Chrome, Safari, or Edge\n3. Allow microphone permissions\n\nOr build the app with EAS Build for native voice support.',
          [{ text: 'OK' }]
        );
        return;
      }

      const started = await VoiceService.startListening({
        onStart: () => {
          setIsListening(true);
          setVoiceText('');
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        },
        onResult: (result) => {
          if (result.isFinal && result.text.trim()) {
            setVoiceText(result.text);
            handleInputTextChange(result.text);
            setIsListening(false);
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
        },
        onPartialResult: (text) => {
          setVoiceText(text);
        },
        onEnd: () => {
          setIsListening(false);
        },
        onError: (error) => {
          setIsListening(false);
          setVoiceText('');
          Alert.alert('Voice Recognition Error', error);
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
      });

      if (!started) {
        setIsListening(false);
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
      Alert.alert('Error', 'Failed to start voice recognition');
    }
  };

  const stopVoiceRecognition = async () => {
    try {
      await VoiceService.stopListening();
      setIsListening(false);
      setVoiceText('');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
      setIsListening(false);
      setVoiceText('');
    }
  };

  // Action handlers for AI responses
  const handleCopyResponse = async (responseText: string) => {
    try {
      await Clipboard.setStringAsync(responseText);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      Alert.alert('Copied!', 'Response copied to clipboard');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      Alert.alert('Error', 'Failed to copy response');
    }
  };

  const handleLikeResponse = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Thanks!', 'Your feedback helps improve NutriAI');
  };

  const handleShareResponse = async (responseText: string) => {
    try {
      console.log('📤 Attempting to share response using native Share API...');

      // Create a proper share message
      const shareMessage = `NutriAI Response:\n\n${responseText}\n\n--- Shared from NutriAI 🍃 ---`;

      // Use React Native's built-in Share API for better cross-platform support
      const result = await Share.share({
        message: shareMessage,
        title: 'NutriAI Response',
      }, {
        dialogTitle: 'Share NutriAI Response',
        subject: 'NutriAI Nutrition Advice', // For email sharing
      });

      if (result.action === Share.sharedAction) {
        console.log('✅ Response shared successfully');
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      } else if (result.action === Share.dismissedAction) {
        console.log('📱 Share dialog dismissed by user');
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }

    } catch (error) {
      console.error('❌ Error sharing response:', error);

      // Fallback to clipboard if sharing fails
      try {
        await Clipboard.setStringAsync(responseText);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        Alert.alert(
          'Copied to Clipboard',
          'Sharing failed, but the response was copied to your clipboard. You can now paste it anywhere to share.',
          [{ text: 'OK', style: 'default' }]
        );
        console.log('📋 Response copied to clipboard as fallback');
      } catch (clipboardError) {
        console.error('❌ Clipboard fallback also failed:', clipboardError);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        Alert.alert(
          'Share Failed',
          'Unable to share or copy the response. Please try again.',
          [{ text: 'OK', style: 'default' }]
        );
      }
    }
  };

  const handleMenuPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Menu Options',
      'Choose an action',
      [
        { text: 'Clear Chat', onPress: () => setMessages([]) },
        { text: 'Settings', onPress: () => Alert.alert('Settings', 'Coming soon!') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  // Premium scroll handler
  const scrollHandler = useAnimatedScrollHandler({
    onScroll: (event) => {
      scrollY.value = event.contentOffset.y;
      headerOpacity.value = interpolate(
        scrollY.value,
        [0, 100],
        [1, 0.9],
        'clamp'
      );
    },
  });

  // Premium container style - REMOVED keyboard transform to fix input focus issues
  const containerStyle = useAnimatedStyle(() => ({
    // Removed keyboard transform that was causing TextInput to lose focus
  }));

  const headerStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
    transform: [{ translateY: interpolate(scrollY.value, [0, 100], [0, -10], 'clamp') }],
  }));

  // Memoized callbacks to prevent unnecessary re-renders
  const handleInputTextChange = useCallback((text: string) => {
    setInputText(text);
  }, []);

  const handleSendMessage = useCallback(() => {
    sendMessage(inputText);
  }, [inputText]);

  const handleStartVoice = useCallback(() => {
    startVoiceRecognition();
  }, []);

  const handleStopVoice = useCallback(() => {
    stopVoiceRecognition();
  }, []);

  return (
    <View style={styles.premiumContainer}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />

      <Animated.View style={[styles.premiumMainContainer, containerStyle]}>
        {/* Premium Header */}
        <Animated.View style={[styles.premiumHeader, headerStyle]} entering={FadeInDown.duration(600)}>
          <SafeAreaView edges={['top']}>
            <View style={styles.premiumHeaderContent}>
              <View style={styles.premiumHeaderTitleSection}>
                <Text style={styles.premiumHeaderTitle}>Ask AI</Text>
                <Text style={styles.premiumHeaderSubtitle}>Nutrition Assistant 🍃</Text>
              </View>
              <TouchableOpacity
                style={styles.premiumHeaderMenuButton}
                onPress={handleMenuPress}
              >
                <Ionicons name="ellipsis-horizontal" size={24} color="#6B7C5A" />
              </TouchableOpacity>
            </View>
          </SafeAreaView>
        </Animated.View>
        {/* Premium Messages Container */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.premiumScrollContainer}
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="none"
        >
          <>
            {messages.length === 0 ? (
            // Welcome Screen with Quick Questions
            <Animated.View entering={FadeInUp.delay(300).duration(800)} style={styles.welcomeSection}>
              <View style={styles.welcomeCard}>
                <View style={styles.welcomeIcon}>
                  <Ionicons name="chatbubble-ellipses" size={40} color={Colors.brand} />
                </View>
                <Text style={styles.welcomeTitle}>Welcome to AI Nutritionist!</Text>
                <Text style={styles.welcomeDescription}>
                  Ask me anything about nutrition, diet, and healthy eating. I'm here to help you make informed decisions about your health.
                </Text>
              </View>

              <View style={styles.quickQuestionsSection}>
                <Text style={styles.quickQuestionsTitle}>Popular Questions</Text>
                <View style={styles.quickQuestionsGrid}>
                  {quickQuestions.map((question, index) => (
                    <QuickQuestion
                      key={question.id}
                      question={question}
                      onPress={() => handleQuickQuestion(question.text)}
                      index={index}
                    />
                  ))}
                </View>
              </View>

              <View style={styles.featuresSection}>
                <Text style={styles.featuresTitle}>I can help you with:</Text>
                <View style={styles.featuresList}>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Personalized nutrition advice</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Meal planning and recipes</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Dietary restrictions guidance</Text>
                  </View>
                  <View style={styles.feature}>
                    <Ionicons name="checkmark-circle" size={20} color={Colors.success} />
                    <Text style={styles.featureText}>Supplement recommendations</Text>
                  </View>
                </View>
              </View>
            </Animated.View>
          ) : (
            // Chat Messages
            <>
              {messages.map((message) => (
                <PremiumMessageBubble
                  key={message.id}
                  message={message}
                />
              ))}

              {/* Loading Indicator when AI is thinking */}
              {loading && !isTyping && (
                <View style={styles.typingIndicatorContainer}>
                  <TypingIndicator visible={true} />
                </View>
              )}

              {/* Typing Message Display */}
              {isTyping && typingText && (
                <View style={styles.typingMessageContainer}>
                  <View style={styles.responseHeader}>
                    <View style={styles.cleanAiAvatar}>
                      <Image
                        source={require('../../assets/image final.png')}
                        style={styles.cleanAiAvatarLogo}
                        resizeMode="contain"
                      />
                    </View>
                    <View style={styles.responseInfo}>
                      <Text style={styles.aiLabel}>NutriAI</Text>
                      <Text style={styles.responseTimestamp}>Responding...</Text>
                    </View>
                  </View>
                  <View style={styles.responseContent}>
                    <Text style={styles.responseText}>{typingText}</Text>
                  </View>
                </View>
              )}
            </>
          )}

          {/* Removed old typing indicator - using smooth animation instead */}
          </>
        </ScrollView>

        {/* Voice Input Overlay */}
        {isListening && (
          <VoiceInput
            isListening={isListening}
            onStartListening={startVoiceRecognition}
            onStopListening={stopVoiceRecognition}
            voiceText={voiceText}
          />
        )}

        {/* Removed fixed quick suggestions */}

        {/* Premium Input Section */}
        <PremiumInputSection
          inputText={inputText}
          setInputText={handleInputTextChange}
          loading={loading}
          isListening={isListening}
          onSendMessage={handleSendMessage}
          onStartVoice={handleStartVoice}
          onStopVoice={handleStopVoice}
        />
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  backgroundImage: {
    flex: 1,
  },
  backgroundImageStyle: {
    opacity: 0.3,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.92)',
  },
  keyboardView: {
    flex: 1,
  },

  // Billion-Dollar Header
  header: {
    marginBottom: 24,
  },
  headerGradient: {
    paddingTop: 80,
    paddingBottom: 24,
    paddingHorizontal: 24,
    borderBottomLeftRadius: 32,
    borderBottomRightRadius: 32,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 12,
  },
  aiHeaderIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    flex: 1,
    marginLeft: Spacing.lg,
  },
  title: {
    fontSize: 24,
    fontWeight: '800',
    color: 'white',
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  subtitle: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  headerActions: {
    marginLeft: Spacing.lg,
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: BorderRadius.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Messages Container
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    paddingHorizontal: 0, // Removed horizontal padding to give full width to responses
    paddingBottom: Spacing.xl,
  },
  chatContainer: {
    gap: Spacing.lg,
  },

  // Welcome Section
  welcomeSection: {
    paddingVertical: Spacing.xl,
    paddingHorizontal: Spacing.lg, // Added horizontal padding for welcome content
  },
  welcomeCard: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xxl,
    padding: Spacing.xxxl,
    alignItems: 'center',
    marginBottom: Spacing.xxl,
    ...Shadows.lg,
  },
  welcomeIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  welcomeTitle: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  welcomeDescription: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    textAlign: 'center',
    lineHeight: 24,
  },

  // Quick Questions
  quickQuestionsSection: {
    marginBottom: Spacing.xxl,
  },
  quickQuestionsTitle: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.bold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  quickQuestionsGrid: {
    gap: Spacing.md,
  },
  quickQuestion: {
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.xl,
    ...Shadows.sm,
  },
  quickQuestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  quickQuestionIcon: {
    width: 40,
    height: 40,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.lg,
  },
  quickQuestionText: {
    flex: 1,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    color: Colors.foreground,
  },

  // Features Section
  featuresSection: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    padding: Spacing.xl,
  },
  featuresTitle: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    color: Colors.foreground,
    marginBottom: Spacing.lg,
  },
  featuresList: {
    gap: Spacing.md,
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  featureText: {
    fontSize: FontSizes.base,
    color: Colors.foreground,
    flex: 1,
  },



  // Removed redundant old message styles
  aiAvatar: {
    width: 32,
    height: 32,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
  },
  aiAvatarLogo: {
    width: 24,
    height: 12,
  },

  // Loading Message
  loadingMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: Spacing.xs,
  },
  loadingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
    gap: Spacing.sm,
  },
  loadingText: {
    fontSize: FontSizes.base,
    color: Colors.mutedForeground,
    fontStyle: 'italic',
  },

  // Billion-Dollar Input Section
  inputSection: {
    paddingHorizontal: 24,
    paddingVertical: 20,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: 'rgba(34, 197, 94, 0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  inputContainer: {
    backgroundColor: '#FAFAFA',
    borderRadius: 24,
    borderWidth: 2,
    borderColor: 'rgba(107, 124, 90, 0.2)',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  textInput: {
    flex: 1,
    fontSize: FontSizes.base,
    color: Colors.foreground,
    maxHeight: 100,
    paddingVertical: Spacing.sm,
    marginRight: Spacing.md,
  },
  inputActions: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  voiceInputButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.muted,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.muted,
  },

  // Modern Voice Input Overlay
  voiceInputContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  voiceBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(107, 124, 90, 0.95)',
  },
  voiceTextContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    marginHorizontal: 32,
    marginBottom: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  voiceTextHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  voiceTextLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7C5A',
  },
  voiceText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#1F2937',
    textAlign: 'center',
    lineHeight: 26,
  },
  voiceButtonContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceWave: {
    position: 'absolute',
    borderRadius: 50,
    borderWidth: 2,
  },
  voiceWave1: {
    width: 80,
    height: 80,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  voiceWave2: {
    width: 100,
    height: 100,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  voiceWave3: {
    width: 120,
    height: 120,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  voiceButton: {
    position: 'relative',
    zIndex: 10,
  },
  voiceButtonInner: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 12,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  voiceButtonListening: {
    backgroundColor: '#8B9A7A',
  },
  voiceInstructions: {
    marginTop: 40,
    paddingHorizontal: 32,
  },
  voiceInstructionText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },

  // Removed redundant message container styles


  // Suggestions
  suggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.md,
    marginTop: Spacing.md,
    marginLeft: 44, // Align with AI message
  },
  suggestionChip: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionText: {
    fontSize: FontSizes.sm,
    color: Colors.foreground,
    fontWeight: FontWeights.medium,
  },

  // Typing Indicator
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-start', // Ensure left alignment
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.md, // Add consistent padding
  },
  // Fixed Typing Indicator - prevents center positioning during input
  typingIndicatorFixed: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
    position: 'relative', // Ensure proper positioning
    alignSelf: 'flex-start', // Prevent center alignment
  },
  // Container for typing indicator that prevents center positioning
  typingIndicatorContainer: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    marginBottom: Spacing.lg,
    paddingHorizontal: Spacing.md,
  },
  typingBubble: {
    backgroundColor: Colors.muted,
    borderRadius: BorderRadius.xl,
    borderBottomLeftRadius: BorderRadius.sm,
    padding: Spacing.lg,
    marginLeft: Spacing.md,
  },
  typingDots: {
    flexDirection: 'row',
    gap: Spacing.xs,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.mutedForeground,
  },

  // Modern Clean Header Styles
  modernHeader: {
    backgroundColor: 'white',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(107, 124, 90, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitleSection: {
    flex: 1,
    alignItems: 'center',
  },
  modernHeaderTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    letterSpacing: -0.3,
  },
  modernHeaderSubtitle: {
    fontSize: 13,
    color: '#6B7280',
    fontWeight: '500',
    marginTop: 2,
  },
  headerMenuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Removed unused ultra modern message styles

  // Removed unused structured response styles

  // Removed unused enhanced response styles

  // Clean Modern Response Formatting Styles (keep only what's used)
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    marginTop: 12,
    paddingBottom: 6,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(107, 124, 90, 0.2)',
  },
  headingText: {
    fontSize: 17,
    fontWeight: '700',
    color: '#6B7C5A',
    flex: 1,
    marginBottom: 8,
  },
  paragraphContainer: {
    marginVertical: 6,
  },

  // ✨ CLEAN MODERN RESPONSE STYLES
  cleanResponseContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginVertical: 8,
    marginHorizontal: 0, // REMOVED - No horizontal margins for maximum width
    paddingVertical: 16,
    paddingHorizontal: 16, // Unified padding for all content
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  responseHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cleanAiAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Clean white background instead of green
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cleanAiAvatarLogo: {
    width: 24,
    height: 12,
  },
  responseInfo: {
    flex: 1,
  },
  aiLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  responseTimestamp: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  responseContent: {
    marginBottom: 12,
  },
  responseText: {
    fontSize: 15,
    lineHeight: 24,
    color: '#374151',
    marginBottom: 12,
  },
  boldText: {
    fontWeight: '700',
    color: '#1F2937',
  },
  noteText: {
    fontStyle: 'italic',
    color: '#6B7280',
    fontSize: 14,
  },
  listSection: {
    marginBottom: 12,
  },
  cleanListItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  listDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#6B7C5A',
    marginTop: 10,
    marginRight: 12,
  },
  listText: {
    fontSize: 15,
    lineHeight: 24,
    color: '#374151',
    flex: 1,
  },
  responseActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  actionBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Removed unused quick suggestions styles

  // User Message Styles
  userMessageContainer: {
    alignSelf: 'flex-end',
    backgroundColor: '#6B7C5A',
    borderRadius: 16,
    borderBottomRightRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginVertical: 4,
    marginHorizontal: 12,
    maxWidth: '80%',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  userMessageText: {
    fontSize: 15,
    color: 'white',
    lineHeight: 20,
    fontWeight: '500',
  },

  // Typing Message Styles
  typingMessageContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    borderBottomLeftRadius: 4,
    marginBottom: 16,
    marginRight: 40,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  typingCursor: {
    width: 2,
    height: 16,
    backgroundColor: Colors.brand,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    marginLeft: 2,
    opacity: 1,
  },

  // Premium Typing Indicator Styles
  premiumTypingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginVertical: 8,
    marginHorizontal: 16,
    paddingBottom: 4,
  },
  premiumAiAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 4,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 3,
  },
  premiumAvatarGradient: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  premiumAvatarLogo: {
    width: 18,
    height: 18,
    tintColor: 'white',
  },
  premiumTypingBubble: {
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  premiumTypingGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    minWidth: 60,
  },
  premiumTypingDots: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  premiumTypingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#6B7C5A',
  },

  // Premium Message Bubble Styles
  premiumMessageContainer: {
    marginVertical: 4,
    marginHorizontal: 16,
  },
  premiumUserBubble: {
    alignSelf: 'flex-end',
    backgroundColor: '#6B7C5A',
    borderRadius: 16,
    borderBottomRightRadius: 4,
    paddingHorizontal: 16,
    paddingVertical: 12,
    maxWidth: '85%',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 3,
  },
  premiumUserText: {
    fontSize: 16,
    color: 'white',
    lineHeight: 22,
    fontWeight: '500',
  },
  premiumAiBubbleContainer: {
    alignSelf: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: '90%',
  },
  premiumAiBubble: {
    borderRadius: 16,
    borderBottomLeftRadius: 4,
    marginLeft: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  premiumAiBubbleGradient: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 16,
    borderBottomLeftRadius: 4,
  },
  premiumAiText: {
    fontSize: 16,
    color: '#1F2937',
    lineHeight: 24,
    fontWeight: '400',
  },
  premiumTimestamp: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    textAlign: 'right',
  },
  premiumMessageActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  premiumActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },

  // Premium Input Styles
  premiumInputContainer: {
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: Platform.OS === 'ios' ? 34 : 12,
  },
  premiumInputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F9FAFB',
    borderRadius: 24,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    paddingHorizontal: 16,
    paddingVertical: 8,
    minHeight: 48,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  premiumInputWrapperFocused: {
    borderColor: '#6B7C5A',
    backgroundColor: '#FFFFFF',
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 2,
  },
  premiumTextInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    lineHeight: 22,
    maxHeight: 120,
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  premiumSendButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#6B7C5A',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  premiumVoiceButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#8B9A7A',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: '#8B9A7A',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  premiumVoiceButtonActive: {
    backgroundColor: '#EF4444',
    shadowColor: '#EF4444',
  },

  // Premium Container Styles
  premiumContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  premiumMainContainer: {
    flex: 1,
  },
  premiumHeader: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  premiumHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  premiumHeaderTitleSection: {
    flex: 1,
  },
  premiumHeaderTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    letterSpacing: -0.5,
  },
  premiumHeaderSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
    fontWeight: '500',
  },
  premiumHeaderMenuButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  premiumScrollContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  premiumEmptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  premiumEmptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  premiumEmptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 8,
  },
  premiumEmptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Premium Response Text Formatting Styles
  premiumResponseHeader: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 8,
    marginTop: 12,
  },
  premiumBoldText: {
    fontWeight: '700',
    color: '#1F2937',
  },
  premiumListItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    paddingLeft: 8,
  },
  premiumListDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#6B7C5A',
    marginTop: 10,
    marginRight: 8,
  },
  premiumListText: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
  },
});

export default AskScreenModern;
