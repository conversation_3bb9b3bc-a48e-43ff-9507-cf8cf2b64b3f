import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  StatusBar,
  RefreshControl,
  ImageBackground,
  TextInput,
  Image,
  Alert,
  Platform,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeInUp,
  FadeInDown,
  SlideInLeft,
  SlideInRight,
  BounceIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
} from 'react-native-reanimated';
import { useNavigation, useFocusEffect } from '@react-navigation/native';

import { useProfile } from '../contexts/ProfileContext';
import { Colors } from '../constants/Colors';
import { Typography } from '../constants/Typography';
import LottieIcon from '../components/LottieIcon';
import { CircularProgress, NutritionProgress } from '../components/CircularProgress';
import HealthMonitor from '../components/HealthMonitor';

import * as Haptics from 'expo-haptics';
import ApiService from '../services/ApiService';
import WeightGoalTracker, { WeightProgress } from '../services/WeightGoalTracker';
import RecipeCacheService from '../services/RecipeCacheService';

const HomeScreenModern: React.FC = () => {
  const navigation = useNavigation();
  const { profile, dailyData, incrementWater, getProgressPercentage, getRecentMeals } = useProfile();
  const [refreshing, setRefreshing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [weightProgress, setWeightProgress] = useState<WeightProgress | null>(null);
  const [loadingWeightProgress, setLoadingWeightProgress] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [mealSuggestions, setMealSuggestions] = useState<any[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);

  // Animated values for micro-interactions
  const pulseValue = useSharedValue(1);
  const waterWaveValue = useSharedValue(0);

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);

    // Pulse animation for active elements
    pulseValue.value = withRepeat(
      withSequence(
        withSpring(1.05, { duration: 1000 }),
        withSpring(1, { duration: 1000 })
      ),
      -1,
      true
    );

    // Water wave animation
    waterWaveValue.value = withRepeat(
      withSequence(
        withSpring(1, { duration: 2000 }),
        withSpring(0.8, { duration: 2000 })
      ),
      -1,
      true
    );

    return () => {
      // Clean up timer
      clearInterval(timer);

      // Stop animations to prevent memory leaks
      pulseValue.value = withSpring(1);
      waterWaveValue.value = withSpring(0);
    };
  }, []);

  // Load profile image and meal suggestions
  useEffect(() => {
    const loadProfileImage = async () => {
      try {
        const savedImage = await AsyncStorage.getItem('profileImage');
        if (savedImage) {
          setProfileImage(savedImage);
        }
      } catch (error) {
        console.error('Error loading profile image:', error);
      }
    };

    const loadMealSuggestions = async () => {
      try {
        const savedSuggestions = await AsyncStorage.getItem('mealSuggestions');
        if (savedSuggestions) {
          setMealSuggestions(JSON.parse(savedSuggestions));
        } else {
          generateMealSuggestions(0); // Start with retry count 0
        }
      } catch (error) {
        console.error('Error loading meal suggestions:', error);
        generateMealSuggestions(0); // Start with retry count 0
      }
    };

    const loadWeightProgress = async () => {
      try {
        setLoadingWeightProgress(true);
        const progress = await WeightGoalTracker.calculateWeightProgress();
        setWeightProgress(progress);
        console.log('📊 Weight progress loaded:', progress);
      } catch (error) {
        console.error('❌ Error loading weight progress:', error);
      } finally {
        setLoadingWeightProgress(false);
      }
    };

    loadProfileImage();
    loadMealSuggestions();
    loadWeightProgress();
  }, []);

  // Refresh meal suggestions when profile changes (with debouncing and proper cleanup)
  useEffect(() => {
    if (!profile.isProfileComplete) return;

    console.log('🔄 Profile changed, scheduling meal suggestions regeneration...');

    let isMounted = true; // Track component mount status

    // Debounce meal suggestions regeneration to prevent excessive API calls
    const debounceTimer = setTimeout(() => {
      if (isMounted) {
        console.log('🔄 Executing debounced meal suggestions regeneration...');
        generateMealSuggestions(0); // Start with retry count 0
      }
    }, 2000); // 2 second debounce delay

    return () => {
      isMounted = false; // Mark as unmounted
      clearTimeout(debounceTimer);
    };
  }, [profile.caloriesGoal, profile.proteinGoal, profile.dietaryPreferences, profile.isProfileComplete]);

  // Refresh data when screen comes into focus (e.g., returning from scanner)
  useFocusEffect(
    React.useCallback(() => {
      // Force a re-render to pick up any new recent meals
      setCurrentTime(new Date());
    }, [])
  );

  const onRefresh = async () => {
    setRefreshing(true);
    setTimeout(() => setRefreshing(false), 1200);
  };

  const getPersonalizedGreeting = () => {
    const hour = new Date().getHours();
    const firstName = profile.name ? profile.name.split(' ')[0] : 'User'; // Get first name only

    let timeGreeting;
    if (hour < 12) {
      timeGreeting = 'Good Morning';
    } else if (hour < 17) {
      timeGreeting = 'Good Afternoon';
    } else {
      timeGreeting = 'Good Evening';
    }

    return `${timeGreeting}, ${firstName}`;
  };

  const addWater = () => {
    incrementWater();
  };



  const waterWaveStyle = useAnimatedStyle(() => ({
    transform: [{ scale: waterWaveValue.value }],
  }));

  const quickActions = [
    {
      icon: 'scan' as const,
      lottieIcon: 'scanner',
      label: 'Smart Scan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Scanner');
      },
    },
    {
      icon: 'restaurant' as const,
      lottieIcon: 'recipes',
      label: 'AI Recipes',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Recipes');
      },
    },
    {
      icon: 'calendar' as const,
      lottieIcon: 'plan',
      label: 'Meal Plan',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Plan');
      },
    },
    {
      icon: 'chatbubble-ellipses' as const,
      lottieIcon: 'aiThinking',
      label: 'Ask AI',
      color: Colors.brand,
      bgColor: Colors.glassGreenLight,
      borderColor: Colors.brandLight,
      onPress: () => {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        (navigation as any).navigate('Ask');
      },
    },
  ];



  // Get real recent meals from AsyncStorage (limit to 3 latest)
  const recentMeals = getRecentMeals()
    .slice(0, 3) // Limit to 3 most recent meals
    .map(meal => ({
      ...meal,
      icon: meal.type === 'breakfast' ? 'sunny' :
            meal.type === 'lunch' ? 'restaurant' :
            meal.type === 'dinner' ? 'moon' : 'cafe'
    }));

  // Enhanced meal suggestions generation with automatic retry
  const generateMealSuggestions = async (retryCount = 0) => {
    if (!profile.isProfileComplete) {
      console.log('⚠️ Profile not complete, skipping meal suggestions');
      setMealSuggestions([]);
      return;
    }

    setLoadingSuggestions(true);
    console.log(`🍽️ Starting meal suggestions generation... (attempt ${retryCount + 1})`);

    const MAX_RETRIES = 2; // Allow 2 automatic retries

    // Check network connectivity first
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const testResponse = await fetch('https://www.google.com', {
        method: 'HEAD',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!testResponse.ok) {
        throw new Error('Network connectivity test failed');
      }
      console.log('✅ Network connectivity confirmed');
    } catch (networkError) {
      console.error('❌ Network connectivity issue:', networkError);
      setLoadingSuggestions(false);
      Alert.alert(
        'No Internet Connection',
        'Please check your internet connection and try again.',
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }

    try {
      const remainingCalories = profile.caloriesGoal - dailyData.caloriesConsumed;
      const remainingProtein = profile.proteinGoal - dailyData.proteinConsumed;

      console.log('📊 Meal suggestion parameters:', {
        remainingCalories: Math.max(remainingCalories, 200),
        remainingProtein: Math.max(remainingProtein, 20),
        dietaryPreferences: profile.dietaryPreferences || [],
        allergies: profile.allergies || [],
        preferredCuisines: profile.preferredCuisines || []
      });

      // Use optimized API call for quick suggestions (not full recipes)
      const suggestions = await ApiService.generateQuickMealSuggestions({
        remainingCalories: Math.max(remainingCalories, 200),
        remainingProtein: Math.max(remainingProtein, 20),
        dietaryPreferences: profile.dietaryPreferences || [],
        allergies: profile.allergies || [],
        preferredCuisines: profile.preferredCuisines || [],
        count: 3
      });

      console.log('✅ Received meal suggestions:', suggestions);
      setMealSuggestions(suggestions);
      setLoadingSuggestions(false);

      // Save suggestions to AsyncStorage for persistence
      await AsyncStorage.setItem('mealSuggestions', JSON.stringify(suggestions));

    } catch (error) {
      console.error(`❌ Error generating meal suggestions (attempt ${retryCount + 1}):`, error);

      // Automatic retry logic
      if (retryCount < MAX_RETRIES) {
        console.log(`🔄 Automatically retrying meal suggestions (${retryCount + 1}/${MAX_RETRIES})...`);

        // Wait a bit before retrying (exponential backoff)
        const delay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
        setTimeout(() => {
          generateMealSuggestions(retryCount + 1);
        }, delay);

        return; // Don't set loading to false yet
      }

      // All retries exhausted - show error to user
      setLoadingSuggestions(false);
      console.error('❌ All retry attempts failed for meal suggestions');

      // Show enhanced error message with retry option
      Alert.alert(
        'Connection Error',
        'Unable to generate personalized meal suggestions after multiple attempts. Please check your internet connection and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Retry Now',
            onPress: () => {
              console.log('🔄 User requested manual retry');
              generateMealSuggestions(0); // Reset retry count for manual retry
            }
          }
        ]
      );

      // Clear suggestions instead of showing fallback data
      setMealSuggestions([]);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={Platform.OS === 'android'}
      />

      {/* Beautiful Background with Image */}
      <ImageBackground
        source={{
          uri: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80'
        }}
        style={styles.backgroundContainer}
        resizeMode="cover"
      >
        <View style={styles.whiteOverlay} />
      </ImageBackground>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={Colors.brand}
            colors={[Colors.brand]}
          />
        }
      >
        {/* Modern Minimal Header - Shadcn x Apple Philosophy */}
        <Animated.View entering={FadeInDown.duration(800)} style={styles.modernHeader}>
          <View style={styles.headerRow}>
            <View style={styles.headerLeft}>
              <View style={styles.logoWrapper}>
                <Image
                  source={require('../../assets/image final.png')}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
              <Text style={styles.modernGreeting}>{getPersonalizedGreeting()}</Text>
              <Text style={styles.modernDate}>{currentTime.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}</Text>
            </View>

            <TouchableOpacity
              style={styles.modernAvatar}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                (navigation as any).navigate('Profile');
              }}
            >
              {profileImage ? (
                <Image source={{ uri: profileImage }} style={styles.avatarImage} />
              ) : (
                <Text style={styles.modernAvatarText}>
                  {profile.name ? profile.name.split(' ').map((n: string) => n[0]).join('') : 'U'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Enhanced Ask AI Section - More Prominent */}
        <Animated.View entering={FadeInUp.delay(200).duration(600)} style={styles.askAIContainer}>
          <TouchableOpacity
            style={styles.askAICard}
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              (navigation as any).navigate('Ask');
            }}
            activeOpacity={0.95}
          >
            <View style={styles.askAIContent}>
              <View style={styles.askAIHeader}>
                <View style={styles.askAIIconContainer}>
                  <LottieIcon
                    name="aiThinking"
                    size={28}
                    color="#FFFFFF"
                    enableHaptics={false}
                  />
                </View>
                <View style={styles.askAITextContainer}>
                  <Text style={styles.askAITitle}>Ask NutriAI</Text>
                  <Text style={styles.askAISubtitle}>Get instant nutrition advice & recipes</Text>
                </View>
                <Ionicons name="chevron-forward" size={24} color={Colors.brand} />
              </View>

              <View style={styles.askAIPrompts}>
                <Text style={styles.askAIPromptText}>What's a healthy breakfast for weight loss?</Text>
                <Text style={styles.askAIPromptText}>Plan my meals for today</Text>
                <Text style={styles.askAIPromptText}>Analyze my nutrition goals</Text>
              </View>
            </View>
          </TouchableOpacity>
        </Animated.View>

        {/* Weight Goal Progress Section */}
        {weightProgress && (
          <Animated.View entering={FadeInUp.delay(500).duration(600)} style={styles.weightGoalCard}>
            <View style={styles.weightGoalHeader}>
              <View style={styles.weightGoalTitleContainer}>
                <Ionicons name="trending-down" size={24} color="#6B7C5A" />
                <Text style={styles.weightGoalTitle}>Weight Goal Progress</Text>
              </View>
              <Text style={styles.weightGoalPercentage}>
                {Math.round(weightProgress.progressPercentage)}%
              </Text>
            </View>

            <View style={styles.weightGoalStats}>
              <View style={styles.weightGoalStat}>
                <Text style={styles.weightGoalStatValue}>
                  {weightProgress.currentEstimatedWeight.toFixed(1)} kg
                </Text>
                <Text style={styles.weightGoalStatLabel}>Current Weight</Text>
              </View>

              <View style={styles.weightGoalStat}>
                <Text style={styles.weightGoalStatValue}>
                  {weightProgress.totalWeightLost.toFixed(1)} kg
                </Text>
                <Text style={styles.weightGoalStatLabel}>Lost</Text>
              </View>

              <View style={styles.weightGoalStat}>
                <Text style={styles.weightGoalStatValue}>
                  {weightProgress.remainingWeightToLose.toFixed(1)} kg
                </Text>
                <Text style={styles.weightGoalStatLabel}>Remaining</Text>
              </View>
            </View>

            <View style={styles.weightGoalProgressBar}>
              <View
                style={[
                  styles.weightGoalProgressFill,
                  { width: `${Math.min(100, weightProgress.progressPercentage)}%` }
                ]}
              />
            </View>

            <View style={styles.weightGoalFooter}>
              <View style={styles.weightGoalStatusContainer}>
                <Ionicons
                  name={weightProgress.isOnTrack ? "checkmark-circle" : "warning"}
                  size={16}
                  color={weightProgress.isOnTrack ? "#6B7C5A" : "#F59E0B"}
                />
                <Text style={[
                  styles.weightGoalStatus,
                  { color: weightProgress.isOnTrack ? "#6B7C5A" : "#F59E0B" }
                ]}>
                  {weightProgress.isOnTrack ? "On Track" : "Needs Attention"}
                </Text>
              </View>
              <Text style={styles.weightGoalDays}>
                {weightProgress.daysRemaining} days left
              </Text>
            </View>
          </Animated.View>
        )}

        {/* Health Monitor - Heart Rate & Steps */}
        <HealthMonitor />



        {/* Nutrition Overview - Real Data - COMPLETELY SEPARATE CONTAINERS */}
        <Animated.View entering={FadeInUp.delay(600).duration(600)} style={styles.healthMetricsCard}>
          <Text style={styles.sectionTitle}>Today's Nutrition</Text>
        </Animated.View>

        {/* FIRST ROW - Calories & Protein - SEPARATE CONTAINER */}
        <View style={styles.nutritionRowContainer}>
          {/* Calories */}
          <Animated.View entering={SlideInLeft.delay(700).duration(600)} style={styles.nutritionItem}>
            <View style={styles.metricHeader}>
              <Ionicons name="flame" size={20} color={Colors.brand} />
              <Text style={styles.metricTitle}>Calories</Text>
            </View>
            <Text style={styles.metricValue}>{Math.round(dailyData.caloriesConsumed)}</Text>
            <Text style={styles.metricUnit}>/ {profile.caloriesGoal}</Text>
            <View style={styles.progressBar}>
              <Animated.View
                entering={SlideInLeft.delay(800).duration(1000)}
                style={[
                  styles.progressFill,
                  {
                    width: `${getProgressPercentage('calories')}%`,
                    backgroundColor: Colors.brand
                  }
                ]}
              />
            </View>
          </Animated.View>

          {/* Protein */}
          <Animated.View entering={SlideInRight.delay(900).duration(600)} style={styles.nutritionItem}>
            <View style={styles.metricHeader}>
              <Ionicons name="fitness" size={20} color={Colors.brand} />
              <Text style={styles.metricTitle}>Protein</Text>
            </View>
            <Text style={styles.metricValue}>{Math.round(dailyData.proteinConsumed)}g</Text>
            <Text style={styles.metricUnit}>/ {profile.proteinGoal}g</Text>
            <View style={styles.progressBar}>
              <Animated.View
                entering={SlideInLeft.delay(1000).duration(1000)}
                style={[
                  styles.progressFill,
                  {
                    width: `${getProgressPercentage('protein')}%`,
                    backgroundColor: Colors.brand
                  }
                ]}
              />
            </View>
          </Animated.View>
        </View>

        {/* SECOND ROW - Water & Meals - SEPARATE CONTAINER */}
        <View style={styles.nutritionRowContainer}>
          {/* Water Intake */}
          <Animated.View entering={SlideInLeft.delay(1100).duration(600)} style={styles.nutritionItem}>
            <View style={styles.metricHeader}>
              <Ionicons name="water" size={20} color={Colors.brand} />
              <Text style={styles.metricTitle}>Water</Text>
            </View>
            <Text style={styles.metricValue}>{dailyData.waterConsumed}</Text>
            <Text style={styles.metricUnit}>/ {profile.waterGoal} glasses</Text>
            <View style={styles.waterDots}>
              {[...Array(Math.min(8, profile.waterGoal))].map((_, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.waterDot,
                    waterWaveStyle,
                    { backgroundColor: i < dailyData.waterConsumed ? Colors.brand : Colors.gray300 }
                  ]}
                />
              ))}
            </View>
            <TouchableOpacity style={styles.addWaterBtn} onPress={addWater}>
              <Ionicons name="add" size={16} color="white" />
            </TouchableOpacity>
          </Animated.View>

          {/* Meals Logged */}
          <Animated.View entering={SlideInRight.delay(1200).duration(600)} style={styles.nutritionItem}>
            <View style={styles.metricHeader}>
              <Ionicons name="restaurant" size={20} color={Colors.brand} />
              <Text style={styles.metricTitle}>Meals</Text>
            </View>
            <Text style={styles.metricValue}>{dailyData.mealsLogged.length}</Text>
            <Text style={styles.metricUnit}>logged today</Text>
            <TouchableOpacity
              style={styles.addMealBtn}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                (navigation as any).navigate('Scanner');
              }}
            >
              <Ionicons name="add" size={16} color="white" />
              <Text style={styles.addMealText}>Add Meal</Text>
            </TouchableOpacity>
          </Animated.View>
        </View>

        {/* Recent Meals */}
        <Animated.View entering={FadeInUp.delay(800).duration(600)} style={styles.recentMealsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Meals</Text>
            <TouchableOpacity
              style={styles.sectionAction}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                (navigation as any).navigate('Scanner');
              }}
            >
              <Text style={styles.sectionActionText}>Log Meal</Text>
            </TouchableOpacity>
          </View>

          {recentMeals.length > 0 ? (
            recentMeals.map((meal, index) => (
              <Animated.View
                key={meal.id}
                entering={SlideInRight.delay(900 + index * 100).duration(500)}
                style={styles.mealItem}
              >
                <View style={styles.mealImageContainer}>
                  <Image
                    source={{
                      uri: `https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=100&h=100&fit=crop&crop=center&q=80`
                    }}
                    style={styles.mealImage}
                    defaultSource={require('../../assets/icon.png')}
                  />
                  <View style={[styles.mealTypeBadge, { backgroundColor:
                    meal.type === 'breakfast' ? '#4CAF50' :
                    meal.type === 'lunch' ? Colors.brand :
                    meal.type === 'dinner' ? '#FF6B35' :
                    '#FFC107'
                  }]}>
                    <Text style={styles.mealTypeBadgeText}>
                      {meal.type.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                </View>
                <View style={styles.mealInfo}>
                  <Text style={styles.mealName}>{meal.name}</Text>
                  <Text style={styles.mealTime}>{meal.time} • {meal.type.charAt(0).toUpperCase() + meal.type.slice(1)}</Text>
                </View>
                <Text style={styles.mealCalories}>{meal.calories > 0 ? `${meal.calories} cal` : ''}</Text>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptyMealsState}>
              <Ionicons name="restaurant-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptyMealsText}>No recent meals</Text>
              <Text style={styles.emptyMealsSubtext}>Your recently added meals will appear here</Text>
            </View>
          )}
        </Animated.View>

        {/* Try These */}
        <Animated.View entering={FadeInUp.delay(1000).duration(600)} style={styles.suggestionsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Try These</Text>
            <TouchableOpacity
              style={styles.sectionAction}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                generateMealSuggestions(0); // Reset retry count for manual refresh
              }}
            >
              <Ionicons
                name="refresh"
                size={16}
                color={Colors.brand}
                style={loadingSuggestions ? styles.spinningIcon : undefined}
              />
              <Text style={styles.sectionActionText}>Refresh</Text>
            </TouchableOpacity>
          </View>

          {loadingSuggestions ? (
            <View style={styles.loadingContainer}>
              <LottieIcon name="loading" size={48} color={Colors.brand} enableHaptics={false} />
              <Text style={styles.loadingText}>Generating suggestions...</Text>
            </View>
          ) : mealSuggestions.length > 0 ? (
            mealSuggestions.map((suggestion, index) => (
              <Animated.View
                key={suggestion.id}
                entering={BounceIn.delay(1100 + index * 100).duration(600)}
                style={styles.suggestionItem}
              >
                <View style={styles.suggestionInfo}>
                  <Text style={styles.suggestionName}>{suggestion.name}</Text>
                  <View style={styles.suggestionMeta}>
                    <Text style={styles.suggestionDetail}>{suggestion.prep}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{suggestion.difficulty}</Text>
                    <Text style={styles.suggestionDot}>•</Text>
                    <Text style={styles.suggestionDetail}>{Math.round(suggestion.calories)} cal</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.suggestionBtn}
                  onPress={async () => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

                    try {
                      // Use RecipeCacheService to get or generate recipe
                      const cachedRecipe = await RecipeCacheService.getOrGenerateRecipe(suggestion.name, 'generated');

                      // Create recipe object compatible with RecipeDetail screen
                      const recipeData = {
                        id: cachedRecipe.id,
                        title: cachedRecipe.title,
                        description: cachedRecipe.description,
                        cookTime: cachedRecipe.cookTime,
                        difficulty: cachedRecipe.difficulty,
                        calories: cachedRecipe.nutrition.calories,
                        ingredients: cachedRecipe.ingredients,
                        instructions: cachedRecipe.instructions,
                        tags: cachedRecipe.tags,
                        imageUrl: cachedRecipe.imageUrl,
                        servings: cachedRecipe.servings
                      };

                      (navigation as any).navigate('RecipeDetail', { recipe: recipeData });
                    } catch (error) {
                      console.error('Error loading recipe:', error);
                      // Fallback navigation with basic data
                      (navigation as any).navigate('RecipeDetail', {
                        recipe: {
                          id: suggestion.id,
                          title: suggestion.name,
                          description: suggestion.description,
                          cookTime: suggestion.prep || '25 min',
                          difficulty: suggestion.difficulty || 'Easy',
                          calories: Math.round(suggestion.calories),
                          ingredients: suggestion.ingredients || [],
                          instructions: ['Recipe details will be generated...'],
                          tags: ['Healthy'],
                          servings: 4
                        }
                      });
                    }
                  }}
                >
                  <Ionicons name="arrow-forward" size={18} color={Colors.brand} />
                </TouchableOpacity>
              </Animated.View>
            ))
          ) : (
            <View style={styles.emptySuggestionsState}>
              <Ionicons name="bulb-outline" size={48} color="#E5E7EB" />
              <Text style={styles.emptySuggestionsText}>
                {!profile.isProfileComplete
                  ? "Complete your profile for personalized suggestions"
                  : "All caught up! You're meeting your goals today."
                }
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Bottom Spacing for Tab Bar */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.backgroundSecondary,
    paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 0,
  },

  // Beautiful Background
  backgroundContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  whiteOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)', // Increased opacity for better visibility
  },

  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 40,
  },

  // Card Base Style
  cardBase: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Header Card
  headerCard: {
    marginTop: 20, // Reduced for mobile app
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.shadowMd,
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },

  // Quick Actions Card
  quickActionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 32,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },



  // Modern Section Header
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  sectionAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.15)',
  },
  sectionActionText: {
    fontSize: 13,
    fontWeight: '700',
    color: '#6B7C5A',
    letterSpacing: 0.3,
  },

  // Modern Section Title
  sectionTitle: {
    ...Typography.headingMedium,
    color: '#6B7C5A',
    marginBottom: 20,
  },

  // Header Section
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  greetingSection: {
    flex: 1,
  },
  greetingText: {
    fontSize: 24,
    fontWeight: '600',
    color: Colors.mutedForeground,
    marginBottom: 4,
    letterSpacing: -0.5,
  },
  nameText: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.foreground,
    letterSpacing: -0.5,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.brand,
    letterSpacing: 0.2,
  },
  profileAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brand,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  avatarText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 1,
  },

  // Progress Bar
  progressBar: {
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
    marginTop: 8,
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },


  nutritionProgressBar: {
    marginTop: 8,
  },
  nutritionProgressTrack: {
    width: '100%',
    height: 6,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 3,
    marginBottom: 8,
    overflow: 'hidden',
  },
  nutritionProgressFill: {
    height: '100%',
    borderRadius: 3,
    shadowColor: '#6B7C5A',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  nutritionTarget: {
    fontSize: 11,
    fontWeight: '600',
    color: 'rgba(107, 124, 90, 0.7)',
    textAlign: 'right',
    letterSpacing: 0.3,
  },

  // Actions Grid - 2x2 Layout
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '48%', // Proper 2x2 grid
    marginBottom: 16,
  },
  actionButton: {
    width: '100%',
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.05)',
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  actionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
    textAlign: 'center',
  },

  // Health Metrics Row - removed duplicate, using the one below

  // Water Card
  waterCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  stepsCard: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.06,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },

  // Health Metrics Card - 2x2 Grid
  healthMetricsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12, // Use gap like profile screen
    marginTop: 16,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    gap: 12,
  },
  metricGridItem: {
    flex: 1, // Take equal space in the row
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    marginBottom: 16,
    minHeight: 120,
  },

  // NEW NUTRITION ROW STYLES - FORCE 2 PER ROW
  nutritionRowContainer: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 10,
    gap: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  nutritionItem: {
    flex: 1, // EXACTLY 50% each - FORCES 2 PER ROW
    backgroundColor: 'rgba(107, 124, 90, 0.05)',
    borderRadius: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(107, 124, 90, 0.1)',
    minHeight: 120,
  },
  heartRateIndicator: {
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginTop: 8,
  },
  heartRateStatus: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7C5A',
    textAlign: 'center',
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  metricTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#4a5568',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    letterSpacing: -0.5,
  },
  metricUnit: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginBottom: 12,
  },
  waterDots: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  waterDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  addWaterBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#4FC3F7',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  addMealBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    alignSelf: 'center',
    marginTop: 8,
  },
  addMealText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },

  // Recent Meals Card
  recentMealsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mealItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mealImageContainer: {
    position: 'relative',
    marginRight: 16,
  },
  mealImage: {
    width: 44,
    height: 44,
    borderRadius: 12,
    backgroundColor: Colors.muted,
  },
  mealTypeBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.background,
  },
  mealTypeBadgeText: {
    fontSize: 10,
    fontWeight: '700',
    color: Colors.white,
  },
  mealInfo: {
    flex: 1,
  },
  mealName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 4,
  },
  mealTime: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  mealCalories: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.brand,
  },

  // Suggestions Card
  suggestionsCard: {
    marginHorizontal: 20,
    marginBottom: 20,
    backgroundColor: Colors.card,
    borderRadius: 24,
    padding: 24,
    shadowColor: Colors.cardShadow,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  spinningIcon: {
    transform: [{ rotate: '45deg' }],
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    marginTop: 12,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  suggestionInfo: {
    flex: 1,
  },
  suggestionName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginBottom: 4,
  },
  suggestionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  suggestionDetail: {
    fontSize: 12,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  suggestionDot: {
    fontSize: 12,
    color: Colors.mutedForeground,
    marginHorizontal: 6,
  },
  suggestionBtn: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: Colors.brandMuted,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Modern Minimal Header Styles (Shadcn x Apple)
  modernHeader: {
    marginHorizontal: 20,
    marginTop: 60,
    marginBottom: 32,
    paddingTop: 20,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerLeft: {
    flex: 1,
    alignItems: 'flex-start',
  },
  logoWrapper: {
    marginBottom: 12,
  },
  logoImage: {
    width: 120,
    height: 48,
  },
  modernGreeting: {
    ...Typography.displaySmall,
    color: Colors.foreground,
    marginBottom: 4,
  },
  modernDate: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.mutedForeground,
    letterSpacing: -0.2,
  },
  modernAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.brandShadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    overflow: 'hidden',
  },
  avatarImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
  },
  modernAvatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.brandForeground,
    letterSpacing: 0.5,
  },

  // Enhanced Ask AI Styles
  askAIContainer: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  askAICard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 15,
  },
  askAIContent: {
    padding: 24,
  },
  askAIHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  askAIIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: Colors.brand,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  askAITextContainer: {
    flex: 1,
  },
  askAITitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.brand,
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  askAISubtitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.mutedForeground,
  },
  askAIPrompts: {
    gap: 12,
    marginTop: 8,
  },
  askAIPromptText: {
    fontSize: 14,
    fontWeight: '400',
    color: Colors.mutedForeground,
    lineHeight: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: 'rgba(107, 124, 90, 0.08)',
    borderRadius: 12,
    borderLeftWidth: 3,
    borderLeftColor: Colors.brand,
  },

  // Bottom Spacing
  bottomSpacing: {
    height: 40,
  },

  // Empty states
  emptyMealsState: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  emptyMealsText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.foreground,
    marginTop: 12,
  },
  emptyMealsSubtext: {
    fontSize: 14,
    color: Colors.mutedForeground,
    marginTop: 4,
  },
  emptySuggestionsState: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: Colors.backgroundSecondary,
    borderRadius: 16,
    padding: 24,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  emptySuggestionsText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.foreground,
    marginTop: 12,
    textAlign: 'center',
    paddingHorizontal: 20,
  },

  // Weight Goal Styles
  weightGoalCard: {
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: '#8B9A7A',
  },
  weightGoalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  weightGoalTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  weightGoalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#6B7C5A',
  },
  weightGoalPercentage: {
    fontSize: 24,
    fontWeight: '800',
    color: '#6B7C5A',
  },
  weightGoalStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  weightGoalStat: {
    alignItems: 'center',
    flex: 1,
  },
  weightGoalStatValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#6B7C5A',
    marginBottom: 4,
  },
  weightGoalStatLabel: {
    fontSize: 12,
    color: '#8B9A7A',
    textAlign: 'center',
  },
  weightGoalProgressBar: {
    height: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 4,
    marginBottom: 16,
    overflow: 'hidden',
  },
  weightGoalProgressFill: {
    height: '100%',
    backgroundColor: '#6B7C5A',
    borderRadius: 4,
  },
  weightGoalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  weightGoalStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  weightGoalStatus: {
    fontSize: 14,
    fontWeight: '600',
  },
  weightGoalDays: {
    fontSize: 14,
    color: '#8B9A7A',
    fontWeight: '500',
  },
});

export default HomeScreenModern;
